"""
Multi-energy CT Image Module - DICOM PS3.3 C.8.2.2

The Multi-energy CT Image Module contains attributes that describe a Multi-energy CT image.
"""
from datetime import datetime
from .base_module import BaseModule
from ..validators.multi_energy_ct_image_validator import MultiEnergyCTImageValidator
from ..validators.base_validator import ValidationConfig


class MultiEnergyCTImageModule(BaseModule):
    """Multi-energy CT Image Module implementation for DICOM PS3.3 C.8.2.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that describe a Multi-energy CT image with complex
    nested sequences for X-Ray sources, detectors, and acquisition paths.
    
    Usage:
        # Create with required elements
        multi_energy_ct = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=[
                MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                    x_ray_source_sequence=[
                        MultiEnergyCTImageModule.create_x_ray_source_item(
                            x_ray_source_index=1,
                            x_ray_source_id="SOURCE_001",
                            multi_energy_source_technique="SWITCHING_SOURCE",
                            source_start_datetime="20240101120000.000000",
                            source_end_datetime="20240101120030.000000",
                            switching_phase_number=1
                        )
                    ],
                    x_ray_detector_sequence=[
                        MultiEnergyCTImageModule.create_x_ray_detector_item(
                            x_ray_detector_index=1,
                            x_ray_detector_id="DETECTOR_001",
                            multi_energy_detector_type="PHOTON_COUNTING",
                            nominal_max_energy=120.0,
                            nominal_min_energy=20.0
                        )
                    ],
                    path_sequence=[
                        MultiEnergyCTImageModule.create_path_item(
                            path_index=1,
                            referenced_x_ray_source_index=1,
                            referenced_x_ray_detector_index=1
                        )
                    ]
                )
            ]
        )
        
        # Add optional elements
        multi_energy_ct.with_optional_elements(
            multi_energy_acquisition_description="Dual-energy CT acquisition"
        )
        
        # Validate
        result = multi_energy_ct.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 1 elements as positional args (user MUST provide value)
        multi_energy_ct_acquisition_sequence: list[dict[str, any]]
    ) -> 'MultiEnergyCTImageModule':
        """Create Multi-energy CT Image Module with all required (Type 1) elements.
        
        Args:
            multi_energy_ct_acquisition_sequence: Multi-energy CT acquisition attributes (0018,9362) Type 1
        """
        instance = cls()
        
        # Set Type 1 elements
        instance.MultiEnergyCTAcquisitionSequence = multi_energy_ct_acquisition_sequence
        
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        multi_energy_acquisition_description: str | None = None
    ) -> 'MultiEnergyCTImageModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('MultiEnergyAcquisitionDescription', multi_energy_acquisition_description)
        
        return self
    
    @staticmethod
    def create_multi_energy_acquisition_item(
        x_ray_source_sequence: list[dict[str, any]],
        x_ray_detector_sequence: list[dict[str, any]],
        path_sequence: list[dict[str, any]],
        multi_energy_acquisition_description: str | None = None
    ) -> dict[str, any]:
        """Create Multi-energy CT Acquisition Sequence item.
        
        Args:
            x_ray_source_sequence: Multi-energy CT X-Ray Source Sequence (0018,9365) Type 1
            x_ray_detector_sequence: Multi-energy CT X-Ray Detector Sequence (0018,936F) Type 1
            path_sequence: Multi-energy CT Path Sequence (0018,9379) Type 1
            multi_energy_acquisition_description: Human readable summary (0018,937B) Type 3
            
        Returns:
            Dictionary representing multi-energy acquisition item
        """
        item = {
            'MultiEnergyCTXRaySourceSequence': x_ray_source_sequence,
            'MultiEnergyCTXRayDetectorSequence': x_ray_detector_sequence,
            'MultiEnergyCTPathSequence': path_sequence
        }
        if multi_energy_acquisition_description:
            item['MultiEnergyAcquisitionDescription'] = multi_energy_acquisition_description
        return item
    
    @staticmethod
    def create_x_ray_source_item(
        x_ray_source_index: int,
        x_ray_source_id: str,
        multi_energy_source_technique: str,
        source_start_datetime: str | datetime,
        source_end_datetime: str | datetime,
        switching_phase_number: int | None = None,
        switching_phase_nominal_duration: float | None = None,
        switching_phase_transition_duration: float | None = None,
        generator_power: float | None = None
    ) -> dict[str, any]:
        """Create Multi-energy CT X-Ray Source Sequence item.
        
        Args:
            x_ray_source_index: Identification number of this Item (0018,9366) Type 1
            x_ray_source_id: Identifier of the physical X-Ray source (0018,9367) Type 1
            multi_energy_source_technique: Technique used to acquire Multi-energy data (0018,9368) Type 1
            source_start_datetime: Date and time source was first used (0018,9369) Type 1
            source_end_datetime: Date and time source was last used (0018,936A) Type 1
            switching_phase_number: Number to identify switching phase (0018,936B) Type 1C
            switching_phase_nominal_duration: Duration in target KV (0018,936C) Type 3
            switching_phase_transition_duration: Duration leaving target KV (0018,936D) Type 3
            generator_power: Power in kW going into X-Ray generator (0018,1170) Type 3
            
        Returns:
            Dictionary representing X-Ray source item
        """
        # Format datetime strings if needed
        if isinstance(source_start_datetime, datetime):
            source_start_datetime = source_start_datetime.strftime("%Y%m%d%H%M%S.%f")
        if isinstance(source_end_datetime, datetime):
            source_end_datetime = source_end_datetime.strftime("%Y%m%d%H%M%S.%f")
        
        item = {
            'XRaySourceIndex': x_ray_source_index,
            'XRaySourceID': x_ray_source_id,
            'MultiEnergySourceTechnique': multi_energy_source_technique,
            'SourceStartDateTime': source_start_datetime,
            'SourceEndDateTime': source_end_datetime
        }
        
        # Type 1C: Required if Multi-energy Source Technique is "SWITCHING_SOURCE"
        if multi_energy_source_technique == "SWITCHING_SOURCE" and switching_phase_number is not None:
            item['SwitchingPhaseNumber'] = switching_phase_number
        
        # Type 3 optional elements
        if switching_phase_nominal_duration is not None:
            item['SwitchingPhaseNominalDuration'] = switching_phase_nominal_duration
        if switching_phase_transition_duration is not None:
            item['SwitchingPhaseTransitionDuration'] = switching_phase_transition_duration
        if generator_power is not None:
            item['GeneratorPower'] = generator_power
            
        return item
    
    @staticmethod
    def create_x_ray_detector_item(
        x_ray_detector_index: int,
        x_ray_detector_id: str,
        multi_energy_detector_type: str,
        nominal_max_energy: float | None = None,
        nominal_min_energy: float | None = None,
        x_ray_detector_label: str | None = None,
        effective_bin_energy: float | None = None
    ) -> dict[str, any]:
        """Create Multi-energy CT X-Ray Detector Sequence item.
        
        Args:
            x_ray_detector_index: Identification number of this Item (0018,9370) Type 1
            x_ray_detector_id: Identifier of the physical X-Ray detector (0018,9371) Type 1
            multi_energy_detector_type: Technology used to detect multiple energies (0018,9372) Type 1
            nominal_max_energy: Nominal maximum energy in keV (0018,9374) Type 1C
            nominal_min_energy: Nominal minimum energy in keV (0018,9375) Type 1C
            x_ray_detector_label: Label of this Item (0018,9373) Type 3
            effective_bin_energy: Energy of heterogeneous photon beam (0018,936E) Type 3
            
        Returns:
            Dictionary representing X-Ray detector item
        """
        item = {
            'XRayDetectorIndex': x_ray_detector_index,
            'XRayDetectorID': x_ray_detector_id,
            'MultiEnergyDetectorType': multi_energy_detector_type
        }
        
        # Type 1C: Required if Multi-energy Detector Type is PHOTON_COUNTING
        if multi_energy_detector_type == "PHOTON_COUNTING":
            if nominal_max_energy is not None:
                item['NominalMaxEnergy'] = nominal_max_energy
            if nominal_min_energy is not None:
                item['NominalMinEnergy'] = nominal_min_energy
        else:
            # May be present otherwise
            if nominal_max_energy is not None:
                item['NominalMaxEnergy'] = nominal_max_energy
            if nominal_min_energy is not None:
                item['NominalMinEnergy'] = nominal_min_energy
        
        # Type 3 optional elements
        if x_ray_detector_label is not None:
            item['XRayDetectorLabel'] = x_ray_detector_label
        if effective_bin_energy is not None:
            item['EffectiveBinEnergy'] = effective_bin_energy
            
        return item
    
    @staticmethod
    def create_path_item(
        path_index: int,
        referenced_x_ray_source_index: int,
        referenced_x_ray_detector_index: int
    ) -> dict[str, any]:
        """Create Multi-energy CT Path Sequence item.
        
        Args:
            path_index: Identification number of the element (0018,937A) Type 1
            referenced_x_ray_source_index: References X-Ray Source Index (0018,9377) Type 1
            referenced_x_ray_detector_index: References X-Ray Detector Index (0018,9376) Type 1
            
        Returns:
            Dictionary representing path item
        """
        return {
            'MultiEnergyCTPathIndex': path_index,
            'ReferencedXRaySourceIndex': referenced_x_ray_source_index,
            'ReferencedXRayDetectorIndex': referenced_x_ray_detector_index
        }
    
    @property
    def has_acquisition_description(self) -> bool:
        """Check if multi-energy acquisition description is present.
        
        Returns:
            bool: True if Multi-energy Acquisition Description is present
        """
        return hasattr(self, 'MultiEnergyAcquisitionDescription')
    
    @property
    def acquisition_sequence_count(self) -> int:
        """Get the number of items in Multi-energy CT Acquisition Sequence.
        
        Returns:
            int: Number of acquisition sequence items
        """
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        return len(seq)
    
    @property
    def has_switching_sources(self) -> bool:
        """Check if any X-Ray sources use switching technique.
        
        Returns:
            bool: True if any source uses SWITCHING_SOURCE technique
        """
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        for acq_item in seq:
            source_seq = acq_item.get('MultiEnergyCTXRaySourceSequence', [])
            for source_item in source_seq:
                if source_item.get('MultiEnergySourceTechnique') == "SWITCHING_SOURCE":
                    return True
        return False
    
    @property
    def has_photon_counting_detectors(self) -> bool:
        """Check if any detectors use photon counting technology.
        
        Returns:
            bool: True if any detector uses PHOTON_COUNTING type
        """
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        for acq_item in seq:
            detector_seq = acq_item.get('MultiEnergyCTXRayDetectorSequence', [])
            for detector_item in detector_seq:
                if detector_item.get('MultiEnergyDetectorType') == "PHOTON_COUNTING":
                    return True
        return False
    
    def get_source_count(self) -> int:
        """Get total number of X-Ray sources across all acquisition items.
        
        Returns:
            int: Total number of X-Ray sources
        """
        total = 0
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        for acq_item in seq:
            source_seq = acq_item.get('MultiEnergyCTXRaySourceSequence', [])
            total += len(source_seq)
        return total
    
    def get_detector_count(self) -> int:
        """Get total number of X-Ray detectors across all acquisition items.
        
        Returns:
            int: Total number of X-Ray detectors
        """
        total = 0
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        for acq_item in seq:
            detector_seq = acq_item.get('MultiEnergyCTXRayDetectorSequence', [])
            total += len(detector_seq)
        return total
    
    def get_path_count(self) -> int:
        """Get total number of acquisition paths across all acquisition items.
        
        Returns:
            int: Total number of acquisition paths
        """
        total = 0
        seq = getattr(self, 'MultiEnergyCTAcquisitionSequence', [])
        for acq_item in seq:
            path_seq = acq_item.get('MultiEnergyCTPathSequence', [])
            total += len(path_seq)
        return total
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this Multi-energy CT Image Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return MultiEnergyCTImageValidator.validate(self, config)
