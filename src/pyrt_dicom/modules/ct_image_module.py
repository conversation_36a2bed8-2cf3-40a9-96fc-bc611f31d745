"""
CT Image Module - DICOM PS3.3 C.8.2.1

The CT Image Module contains attributes that describe CT images.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..validators.ct_image_validator import CTImageValidator
from ..validators.base_validator import ValidationConfig


class CTImageModule(BaseModule):
    """CT Image Module implementation for DICOM PS3.3 C.8.2.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that describe CT images including pixel characteristics,
    acquisition parameters, and reconstruction details.
    
    Usage:
        # Create with required elements
        ct_image = CTImageModule.from_required_elements(
            image_type=["ORIGINAL", "PRIMARY", "AXIAL"],
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1
        )
        
        # Add optional elements
        ct_image.with_optional_elements(
            multi_energy_ct_acquisition="NO",
            scan_options="HELICAL_CT",
            data_collection_diameter=500.0,
            convolution_kernel="STANDARD"
        )
        
        # Add conditional elements
        ct_image.with_rescale_type_conditional(
            rescale_type="HU"
        )
        
        # Validate
        result = ct_image.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 1 elements as positional args (user MUST provide value)
        image_type: list[str],
        samples_per_pixel: int,
        photometric_interpretation: str,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        rescale_intercept: float,
        rescale_slope: float,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        kvp: float | str = "",
        acquisition_number: int | str = ""
    ) -> 'CTImageModule':
        """Create CT Image Module with all required (Type 1 and Type 2) elements.
        
        Args:
            image_type: Image identification characteristics (0008,0008) Type 1
            samples_per_pixel: Number of samples (planes) in this image (0028,0002) Type 1
            photometric_interpretation: Intended interpretation of pixel data (0028,0004) Type 1
            bits_allocated: Number of bits allocated for each pixel sample (0028,0100) Type 1
            bits_stored: Number of bits stored for each pixel sample (0028,0101) Type 1
            high_bit: Most significant bit for pixel sample data (0028,0102) Type 1
            rescale_intercept: Value b in relationship between stored values and output units (0028,1052) Type 1
            rescale_slope: Value m in the equation specified in Rescale Intercept (0028,1053) Type 1
            kvp: Peak kilo voltage output of the X-Ray generator used (0018,0060) Type 2
            acquisition_number: Number identifying the single continuous gathering of data (0020,0012) Type 2
        """
        instance = cls()
        
        # Set Type 1 elements
        instance.ImageType = image_type
        instance.SamplesPerPixel = samples_per_pixel
        instance.PhotometricInterpretation = photometric_interpretation
        instance.BitsAllocated = bits_allocated
        instance.BitsStored = bits_stored
        instance.HighBit = high_bit
        instance.RescaleIntercept = rescale_intercept
        instance.RescaleSlope = rescale_slope
        
        # Set Type 2 elements
        instance.KVP = kvp
        instance.AcquisitionNumber = acquisition_number
        
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        multi_energy_ct_acquisition: str | None = None,
        scan_options: str | None = None,
        data_collection_diameter: float | None = None,
        data_collection_center_patient: list[float] | None = None,
        reconstruction_diameter: float | None = None,
        reconstruction_target_center_patient: list[float] | None = None,
        distance_source_to_detector: float | None = None,
        distance_source_to_patient: float | None = None,
        gantry_detector_tilt: float | None = None,
        table_height: float | None = None,
        rotation_direction: str | None = None,
        exposure_time: float | None = None,
        x_ray_tube_current: float | None = None,
        exposure: float | None = None,
        exposure_in_as: float | None = None,
        filter_type: str | None = None,
        filter_material: list[str] | None = None,
        generator_power: float | None = None,
        focal_spots: list[float] | None = None,
        convolution_kernel: str | None = None,
        revolution_time: float | None = None,
        single_collimation_width: float | None = None,
        total_collimation_width: float | None = None,
        table_speed: float | None = None,
        table_feed_per_rotation: float | None = None,
        spiral_pitch_factor: float | None = None,
        exposure_modulation_type: str | None = None,
        ctdivol: float | None = None,
        ctdi_phantom_type_code_sequence: list[dict[str, any]] | None = None,
        water_equivalent_diameter: float | None = None,
        image_and_fluoroscopy_area_dose_product: float | None = None,
        calcium_scoring_mass_factor_patient: float | None = None,
        calcium_scoring_mass_factor_device: list[float] | None = None,
        ct_additional_x_ray_source_sequence: list[dict[str, any]] | None = None
    ) -> 'CTImageModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('MultiEnergyCTAcquisition', multi_energy_ct_acquisition)
        self._set_attribute_if_not_none('ScanOptions', scan_options)
        self._set_attribute_if_not_none('DataCollectionDiameter', data_collection_diameter)
        self._set_attribute_if_not_none('DataCollectionCenterPatient', data_collection_center_patient)
        self._set_attribute_if_not_none('ReconstructionDiameter', reconstruction_diameter)
        self._set_attribute_if_not_none('ReconstructionTargetCenterPatient', reconstruction_target_center_patient)
        self._set_attribute_if_not_none('DistanceSourceToDetector', distance_source_to_detector)
        self._set_attribute_if_not_none('DistanceSourceToPatient', distance_source_to_patient)
        self._set_attribute_if_not_none('GantryDetectorTilt', gantry_detector_tilt)
        self._set_attribute_if_not_none('TableHeight', table_height)
        self._set_attribute_if_not_none('RotationDirection', rotation_direction)
        self._set_attribute_if_not_none('ExposureTime', exposure_time)
        self._set_attribute_if_not_none('XRayTubeCurrent', x_ray_tube_current)
        self._set_attribute_if_not_none('Exposure', exposure)
        self._set_attribute_if_not_none('ExposureInAs', exposure_in_as)
        self._set_attribute_if_not_none('FilterType', filter_type)
        self._set_attribute_if_not_none('FilterMaterial', filter_material)
        self._set_attribute_if_not_none('GeneratorPower', generator_power)
        self._set_attribute_if_not_none('FocalSpots', focal_spots)
        self._set_attribute_if_not_none('ConvolutionKernel', convolution_kernel)
        self._set_attribute_if_not_none('RevolutionTime', revolution_time)
        self._set_attribute_if_not_none('SingleCollimationWidth', single_collimation_width)
        self._set_attribute_if_not_none('TotalCollimationWidth', total_collimation_width)
        self._set_attribute_if_not_none('TableSpeed', table_speed)
        self._set_attribute_if_not_none('TableFeedPerRotation', table_feed_per_rotation)
        self._set_attribute_if_not_none('SpiralPitchFactor', spiral_pitch_factor)
        self._set_attribute_if_not_none('ExposureModulationType', exposure_modulation_type)
        self._set_attribute_if_not_none('CTDIvol', ctdivol)
        self._set_attribute_if_not_none('CTDIPhantomTypeCodeSequence', ctdi_phantom_type_code_sequence)
        self._set_attribute_if_not_none('WaterEquivalentDiameter', water_equivalent_diameter)
        self._set_attribute_if_not_none('ImageAndFluoroscopyAreaDoseProduct', image_and_fluoroscopy_area_dose_product)
        self._set_attribute_if_not_none('CalciumScoringMassFactorPatient', calcium_scoring_mass_factor_patient)
        self._set_attribute_if_not_none('CalciumScoringMassFactorDevice', calcium_scoring_mass_factor_device)
        self._set_attribute_if_not_none('CTAdditionalXRaySourceSequence', ct_additional_x_ray_source_sequence)
        
        return self
    
    def with_rescale_type_conditional(
        self,
        rescale_type: str | None = None
    ) -> 'CTImageModule':
        """Add rescale type with conditional validation.
        
        Type 1C: Required if the Rescale Type is not HU (Hounsfield Units), 
        or Multi-energy CT Acquisition (0018,9361) is YES.
        
        Args:
            rescale_type: Specifies the output units of Rescale Slope and Rescale Intercept (0028,1054) Type 1C
        """
        # Check conditions for Type 1C requirement
        multi_energy_acquisition = getattr(self, 'MultiEnergyCTAcquisition', '')
        condition = (multi_energy_acquisition == "YES" or 
                    (rescale_type and rescale_type != "HU"))
        
        if condition and rescale_type:
            self.RescaleType = rescale_type
        elif condition and not rescale_type:
            # This will be caught by validation
            pass
        elif rescale_type:
            # May be present even when not required
            self.RescaleType = rescale_type
            
        return self
    
    def with_energy_weighting_conditional(
        self,
        energy_weighting_factor: float | None = None,
        derivation_code_sequence: list[dict[str, any]] | None = None
    ) -> 'CTImageModule':
        """Add energy weighting factor with conditional validation.
        
        Type 1C: Required if one Derivation Code Sequence (0008,9215) Item value 
        is "Multi-energy proportional weighting".
        
        Args:
            energy_weighting_factor: Weighting factor of data from primary source (0018,9353) Type 1C
            derivation_code_sequence: Derivation code sequence for validation (0008,9215)
        """
        # Check for multi-energy proportional weighting condition
        has_multi_energy_weighting = False
        if derivation_code_sequence:
            for item in derivation_code_sequence:
                if (item.get('CodeValue') == '113097' and 
                    item.get('CodingSchemeDesignator') == 'DCM'):
                    has_multi_energy_weighting = True
                    break
        
        if has_multi_energy_weighting and energy_weighting_factor is not None:
            self.EnergyWeightingFactor = energy_weighting_factor
        elif energy_weighting_factor is not None:
            # May be present even when not required
            self.EnergyWeightingFactor = energy_weighting_factor
            
        return self
    
    def with_water_equivalent_diameter_conditional(
        self,
        water_equivalent_diameter_calculation_method_code_sequence: list[dict[str, any]] | None = None
    ) -> 'CTImageModule':
        """Add water equivalent diameter calculation method with conditional validation.
        
        Type 1C: Required if Water Equivalent Diameter (0018,1271) is present.
        
        Args:
            water_equivalent_diameter_calculation_method_code_sequence: Method of calculation (0018,1272) Type 1C
        """
        has_water_diameter = hasattr(self, 'WaterEquivalentDiameter')
        
        if has_water_diameter and water_equivalent_diameter_calculation_method_code_sequence:
            self.WaterEquivalentDiameterCalculationMethodCodeSequence = water_equivalent_diameter_calculation_method_code_sequence
        elif has_water_diameter and not water_equivalent_diameter_calculation_method_code_sequence:
            # This will be caught by validation
            pass
            
        return self
    
    @staticmethod
    def create_ctdi_phantom_type_code_item(
        code_value: str,
        coding_scheme_designator: str = "DCM",
        code_meaning: str | None = None
    ) -> dict[str, any]:
        """Create CTDI phantom type code sequence item.
        
        Args:
            code_value: Code value for phantom type
            coding_scheme_designator: Coding scheme designator (default: DCM)
            code_meaning: Human readable meaning of the code
            
        Returns:
            Dictionary representing code sequence item
        """
        item = {
            'CodeValue': code_value,
            'CodingSchemeDesignator': coding_scheme_designator
        }
        if code_meaning:
            item['CodeMeaning'] = code_meaning
        return item
    
    @staticmethod
    def create_ct_additional_x_ray_source_item(
        kvp: float,
        x_ray_tube_current_in_ma: float,
        data_collection_diameter: float,
        focal_spots: list[float],
        filter_type: str,
        filter_material: list[str],
        exposure_in_mas: float | None = None,
        energy_weighting_factor: float | None = None
    ) -> dict[str, any]:
        """Create CT additional X-Ray source sequence item.
        
        Args:
            kvp: Peak kilo voltage output (0018,0060) Type 1
            x_ray_tube_current_in_ma: Nominal X-Ray tube current (0018,9330) Type 1
            data_collection_diameter: Diameter of data collection region (0018,0090) Type 1
            focal_spots: Used nominal size of focal spot (0018,1190) Type 1
            filter_type: Type of filter(s) inserted (0018,1160) Type 1
            filter_material: X-Ray absorbing material used (0018,7050) Type 1
            exposure_in_mas: Exposure in milliampere seconds (0018,9332) Type 3
            energy_weighting_factor: Weighting factor for multi-energy (0018,9353) Type 1C
            
        Returns:
            Dictionary representing additional X-Ray source item
        """
        item = {
            'KVP': kvp,
            'XRayTubeCurrentInmA': x_ray_tube_current_in_ma,
            'DataCollectionDiameter': data_collection_diameter,
            'FocalSpots': focal_spots,
            'FilterType': filter_type,
            'FilterMaterial': filter_material
        }
        if exposure_in_mas is not None:
            item['ExposureInmAs'] = exposure_in_mas
        if energy_weighting_factor is not None:
            item['EnergyWeightingFactor'] = energy_weighting_factor
        return item
    
    @property
    def is_multi_energy(self) -> bool:
        """Check if this is a multi-energy CT acquisition.
        
        Returns:
            bool: True if Multi-energy CT Acquisition is YES
        """
        return getattr(self, 'MultiEnergyCTAcquisition', '') == "YES"
    
    @property
    def is_axial_image(self) -> bool:
        """Check if this is an axial CT image.
        
        Returns:
            bool: True if Image Type Value 3 is AXIAL
        """
        image_type = getattr(self, 'ImageType', [])
        return len(image_type) >= 3 and image_type[2] == "AXIAL"
    
    @property
    def is_localizer_image(self) -> bool:
        """Check if this is a localizer CT image.
        
        Returns:
            bool: True if Image Type Value 3 is LOCALIZER
        """
        image_type = getattr(self, 'ImageType', [])
        return len(image_type) >= 3 and image_type[2] == "LOCALIZER"
    
    @property
    def has_rescale_type(self) -> bool:
        """Check if rescale type is present.
        
        Returns:
            bool: True if Rescale Type is present
        """
        return hasattr(self, 'RescaleType')
    
    @property
    def has_water_equivalent_diameter(self) -> bool:
        """Check if water equivalent diameter is present.
        
        Returns:
            bool: True if Water Equivalent Diameter is present
        """
        return hasattr(self, 'WaterEquivalentDiameter')
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this CT Image Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return CTImageValidator.validate(self, config)
